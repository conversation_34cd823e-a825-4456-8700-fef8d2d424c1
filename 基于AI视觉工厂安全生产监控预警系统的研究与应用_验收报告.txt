基于AI视觉工厂安全生产监控预警系统的研究与应用

课题验收报告

验收编号：HDCZ 2024-007

一、项目名称：基于AI视觉工厂安全生产监控预警系统的研究与应用

二、项目起止时间：2022.01-2024.06

三、项目研究的思路及主要内容

1、研究思路

通过调研观察法，学习各厂家的优点，结合自身的能力进行研发，并请外部专家进行方案论证及评审。

一、需求分析与基础数据采集阶段

工厂安全生产调研

收集厂内生产车间各类事故隐患和安全风险交织叠加的影响因素，建立安全生产风险数据库。

调研传统"视频+人工"监控方式的局限性，分析监控画面多、现场作业环境复杂、监控人员容易出现疏忽、疲劳等情况，明确安全管理工作技术难点。

分析不同作业场景（员工着装、作业过程、重点起重吊装作业、生产机械作业情况、仪表指标等）的安全要素识别需求，记录危险动作、违规作业流程、危险源等高频安全隐患。

梳理传统监控系统在智能化分析上的结构稳定性不足、识别效率低等问题，明确AI视觉技术优化的关键参数。

归纳安全生产监控系统数据处理、预警告警的核心原因，确定算法模型构建的重点方向。

二、方案设计与模型构建阶段

AI视觉监控系统方案设计

基于AI识别技术、传感技术、云计算技术，构建安全生产监控系统的三维架构模型，设定感知层、传输层、应用层的不同功能边界条件。

提出AI视觉人员设备姿态识别技术和图像数据增强技术的多方案设计，建立安全算法策略数据库指标体系（预警准确率、响应速度）。

安全预警系统分析模型

采用人体模型数字抽象化坐标化方法构建人员作业姿态模型，输入不同作业环境参数，模拟人员作业过程中关键特征的参数化、坐标化轨迹。

设计顺序化卷积架构技术，将姿态参数在时间轴线上的对比分析以及分析后的姿态参数与设定姿态参数对比，建立姿态判别分析标准。

智能预警与监控系统集成设计

结合图像数据深度挖掘技术，绘制自动预警系统控制流程图，确定预警策略匹配阈值。

设计声光及智慧语音报警系统的布局方案，明确监控视频数据中心与现场设备的对应关系。

三、技术研发与系统实现阶段

AI视觉技术结构强化研发

利用AI视觉人员设备姿态识别技术进行安全要素分析，对比不同算法的识别准确率，优化图像预处理技术，通过实际应用验证系统稳定性。

开发全新的自动预警方法，实现监控系统根据实时图像数据自动识别分析判断，响应时间≤实时。

监控系统智能化技术开发

基于安全算法策略数据库构建结果，加工视频监控数据中心原型系统，在生产车间进行不同作业场景的监控试验，通过预警准确率采集验证系统性能，迭代优化算法设计。

集成声光及智慧语音报警系统，建立危险源信息-预警内容映射数据库，实现全天候无人值守、实时主动预警。

智能化监控技术集成

搭建视频监控数据中心平台，测试AI视觉解析监控图像的识别效率，优化系统架构设计以提升预警或提醒的反应速度和准确率。

研发自动分析判别全自动主动预警技术，通过判别结果与预警内容自主匹配方法，在作业区域形成全覆盖安全监控，安全作业面覆盖率≥95%。

2、研究内容

(1)升级原有视频监控系统并新增监视点位以达到生产区域多角度覆盖，全方位监视。新建一套作业区内声光及智慧语音报警系统。建立视频监控数据中心，将生产车间内的监控画面全部纳入到视频监控数据中心统一管理。

(2)开发一套基于AI视觉技术的生产过程安全要素分析、主动预警系统。

(3)建立典型生产环节安全作业数据库，建立重点危险源预防策略，用于指导作业人员生产过程中的安全作业，助力工厂安全生产管控。

四、项目取得的主要成果

1、解决的主要问题：传统国内通常采用"视频+人工"方式进行管理，设置控制室，采用人工监控，生产现场采取流动安全管理人员循回检查；由于监控画面多，现场作业环境复杂，监控人员容易出现疏忽、疲劳等情况，现场安全管理人员的局限性，无法做的实时、跟踪管理，导致安全隐患无法有效排除、安全生产问题时有发生。安全生产监控系统可以有效解决安全管理工作不全面的问题。

2、创新点：

(1)该系统采用了AI视觉人员设备姿态识别技术和图像数据增强技术，对采集到的图形进行缩放、矫正、对比增强等，对采集到的图像进行预处理。同时运用了人体模型数字抽象化坐标化的方法，将人员作业过程中的关键特征进行参数化、坐标化。采用了顺序化卷积架构技术，将姿态参数在时间轴线上的对比分析以及分析后的姿态参数与设定姿态参数对比，进行姿态判别分析。

(2)该系统开发了一套全新的自动预警方法，搭建了一套安全算法策略数据库。通过图像数据深度挖掘技术将实时采集的图像进行分析，匹配预制定的安全预警策略，从而形成一套合理的安全预警系统。

(3)该系统采用了自动分析判别全自动主动预警技术，运用了判别结果与预警内容自主匹配的方法。整个安全生产预警系统正常运行时不需要人为干预，做到全天候无人值守，实时主动预警，减少了安全监督人员劳动强度，并提高了安全预警或提醒的反应速度和准确率。

3、推广应用前景和效益分析：

该项目成功实施后可以在国内其他类型的大型生产车间进行推广，大大提高工厂生产过程的安全性，减少安全事故发生率，提高生产企业的安全管理水平。

项目达到的主要技术指标和经济指标：

(1)在选定的作业区内，覆盖安全作业面达到95%；

(2)减少安全管理人力投入，降低人力成本64万/年；

(3)安全信息预警、告警正确率达到92%；

(4)减少因安全风险造成的设备损坏维护费用55万/年，减少非计划停机15%，提高设备运行效率和资产利用率；

(5)提高车间安全生产的管理效率，降低了安全生产成本。

4、项目实施后，取得的成果：

(1)完成厂内生产作业区域内工业电视监视系统的升级改造。

(2)申请相关专利1项。获得一项实用新型专利，《一种车间安全视频监控系统》，专利号：ZL 2023 2 1839468.0。

(3)发表论文2篇。
《工程技术》2022年9月刊《人机混合智能技术在散料装备无人值守系统上的应用》；
《工程技术》2023年4月刊《维度监测与大数据分析诊断系统在散料装备上的应用》；

(4)完成视频监控系统方案及图纸1套。

(5)完成斗轮堆取料机工业电视系统2套。

五、项目成果提交形式

1、完成生产作业区域内工业电视监视系统的升级改造，实现工厂安全生产多方位视频监控，建立工厂作业区内的声光报警及智慧语音提醒系统。建立工厂安全生产视频监控数据中心。

2、获得实用新型专利1项。

3、发表论文2篇。

4、完成视频监控系统方案及图纸1套。

5、完成斗轮堆取料机项目工业电视系统2套。

六、项目的整体技术水平

国内均没有对此项的研究，处于国内领先水平。

七、项目产生的经济、社会效益

该项目成功实施后可以在国内其他类型的大型生产车间进行推广，大大提高工厂生产过程的安全性，减少安全事故发生率，提高生产企业的安全管理水平。

八、项目执行过程中存在的问题和需要说明的情况

存在的问题：

(1)作业面的覆盖程度取决于摄像头的数量以及分辨率；

(2)需要提升安全算法的判别准确性和计算的实时性，才能保证安全信息预警、告警正确率。

九、项目后续需要改进或开展的工作

(1)合理的布置摄像头的安装位置，争取以较少的摄像头便可覆盖全部的作业面；

(2)优化AI视觉安全识别算法，提高安全信息预警、告警正确率。

十、专家组验收意见

2024年7月20日，华电曹妃甸重工装备有限公司在曹妃甸401会议室组织召开了"基于AI视觉工厂安全生产监控预警系统的研究与应用"(项目编号 CHECKJ22-01-26)科技项目验收会。会议组成验收专家组(名单附后)。专家组听取了项目承担单位对项目执行情况的汇报，审查了项目技术研究报告、资金使用等文件，经充分讨论，形成验收意见如下：

一、提供验收的技术文件齐全、规范，符合科技项目验收要求。

二、通过对车间原有的视频监控系统进行升级改造，并新建一套作业区内声光及智慧语音报警系统以及视频监控数据中心，实现视频监控数据中心统一管理，系统中应用到AI视觉人员设备姿态识别技术和图像数据增强技术，搭建安全算法策略数据库、开发一种全新的自动预警方法，实现了视频监控系统全天候无人值守，可实时主动预警，减少了安全监督人员劳动强度，提高了安全预警或提醒的反应速度和准确率。

三、通过常州斗轮机项目，将理论研究与工程实践相结合，为AI视觉工厂安全生产监控预警系统的进一步推广产生积极的促进作用。

四、依托该项目共获得授权专利共1项，发表论文2篇。

专家组认为，该项目已完成预定的各项研究任务，达到了预期的目标，经费使用合理，一致同意通过验收。

验收专家组组长：_________________    日期：2024年7月20日

验收专家组成员：_________________    日期：2024年7月20日
                _________________    日期：2024年7月20日  
                _________________    日期：2024年7月20日
                _________________    日期：2024年7月20日

项目负责人：_________________      日期：2024年7月20日

承担单位（盖章）：_________________  日期：2024年7月20日 