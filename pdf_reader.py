#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文件读取脚本
"""
import os
import sys
import PyPDF2

def read_pdf_text(file_path):
    """读取PDF文件的文本内容"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
    except Exception as e:
        return f"Error reading PDF: {str(e)}"

def main():
    if len(sys.argv) < 2:
        print("Usage: python pdf_reader.py <pdf_file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        sys.exit(1)
    
    print(f"Reading PDF file: {file_path}")
    print("=" * 50)
    
    text = read_pdf_text(file_path)
    print(text)

if __name__ == "__main__":
    main() 